//package ai.yourouter.calculate.model;
//
//import ai.yourouter.common.constant.SystemConstant;
//import ai.yourouter.jpa.system.model.log.repository.SystemModelRequestLogRepository;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//import ai.yourouter.response.system.model.log.MinuteAvgTpsDto;
//
//import java.time.Duration;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//@Slf4j
//@Component
//@RequiredArgsConstructor
//public class MinuteTpsPreheatTask {
//
//    private final SystemModelRequestLogRepository systemModelRequestLogRepository;
//    private final RedisTemplate<String, String> redisTemplate;
//
//
//    /** 每分钟第 5 秒执行：聚合并写 Redis（TTL 2 分钟） */
//    @Scheduled(cron = "5 * * * * *")
//    public void cacheLastMinute() {
//        long now = System.currentTimeMillis();
//        long bucketStart = (now / 60_000) * 60_000 - 60_000; // 上一分钟整点
//        List<MinuteAvgTpsDto> list = systemModelRequestLogRepository.findAvgTpsPerMinute(bucketStart, bucketStart + 60_000);
//        if (list.isEmpty()) return;
//
//        Map<String, String> hash = new HashMap<>(list.size());
//        list.forEach(dto ->
//                hash.put(dto.systemVendor() + ":" + dto.systemModelName(),
//                        dto.avgTps().toString())
//        );
//
//        String key = SystemConstant.KEY_PREFIX + bucketStart;
//        redisTemplate.opsForHash().putAll(key, hash);
//        redisTemplate.expire(key, Duration.ofMinutes(2));
//
//        log.debug("Cached minute TPS {}, fields={}", bucketStart, hash.size());
//    }
//}
