# Vertex AI 视频生成 API 使用指南

## 概述

本文档介绍如何使用 Vertex AI 的 Veo 3.0 视频生成 API。该 API 支持基于文本提示生成高质量视频内容。

## API 端点

### 1. 启动视频生成任务

**端点**: `POST /v1/projects/cognition/locations/us/publishers/google/models/{model}:predictLongRunning`

**模型名称**: `veo-3.0-generate-preview`

**请求示例**:
```json
{
  "instances": [
    {
      "prompt": "A close up of two people staring at a cryptic drawing on a wall, torchlight flickering. A man murmurs, This must be it. That's the secret code.The woman looks at him and whispering excitedly,What did you find?"
    }
  ]
}
```

**响应示例**:
```json
{
  "name": "projects/hopeful-list-467716-e2/locations/us-central1/publishers/google/models/veo-3.0-generate-preview/operations/5e387b5b-b676-4c1b-adbe-4b7e0413f093"
}
```

### 2. 查询操作状态

**端点**: `POST /v1/projects/cognition/locations/us/publishers/google/models/{model}:fetchPredictOperation`

**请求示例**:
```json
{
  "name": "projects/hopeful-list-467716-e2/locations/us-central1/publishers/google/models/veo-3.0-generate-preview/operations/5e387b5b-b676-4c1b-adbe-4b7e0413f093"
}
```

**响应示例（进行中）**:
```json
{
  "name": "projects/hopeful-list-467716-e2/locations/us-central1/publishers/google/models/veo-3.0-generate-preview/operations/5e387b5b-b676-4c1b-adbe-4b7e0413f093",
  "metadata": {
    "@type": "type.googleapis.com/google.cloud.aiplatform.v1.GenericOperationMetadata",
    "createTime": "2025-08-02T12:38:40.123456Z",
    "updateTime": "2025-08-02T12:39:15.789012Z",
    "state": "RUNNING",
    "progressPercentage": 45
  },
  "done": false
}
```

**响应示例（完成）**:
```json
{
  "name": "projects/hopeful-list-467716-e2/locations/us-central1/publishers/google/models/veo-3.0-generate-preview/operations/5e387b5b-b676-4c1b-adbe-4b7e0413f093",
  "metadata": {
    "@type": "type.googleapis.com/google.cloud.aiplatform.v1.GenericOperationMetadata",
    "createTime": "2025-08-02T12:38:40.123456Z",
    "updateTime": "2025-08-02T12:41:22.456789Z",
    "state": "SUCCEEDED",
    "progressPercentage": 100
  },
  "done": true,
  "response": {
    "predictions": [
      {
        "bytesBase64Encoded": "UklGRjIAAABXRUJQVlA4ICYAAACyAgCdASoBAAEALmk0mk0iIiIiIgBoSygABc6zbAAA/v56QAAAAA==",
        "mimeType": "video/mp4",
        "duration": 5.0,
        "width": 1280,
        "height": 720,
        "frameRate": 24.0,
        "sizeBytes": 2048576
      }
    ]
  }
}
```

## 请求参数说明

### instances 参数

- `prompt` (必需): 视频生成的文本描述
- `negativePrompt` (可选): 不希望在视频中出现的内容描述
- `referenceImage` (可选): 参考图像，用于风格指导
- `referenceVideo` (可选): 参考视频，用于动作指导

### parameters 参数

- `duration` (可选): 视频时长（秒），默认 5 秒
- `aspectRatio` (可选): 宽高比，如 "16:9", "9:16", "1:1"
- `seed` (可选): 随机种子，用于可重现的结果
- `quality` (可选): 生成质量，如 "standard", "high"
- `frameRate` (可选): 帧率，默认 24 fps
- `motionIntensity` (可选): 运动强度，如 "low", "medium", "high"
- `cameraMotion` (可选): 相机运动类型

## 使用流程

1. **发起视频生成请求**: 调用 `predictLongRunning` 端点，获取操作名称
2. **轮询操作状态**: 使用操作名称调用 `fetchPredictOperation` 端点
3. **获取结果**: 当 `done` 为 `true` 时，从 `response.predictions` 中获取生成的视频

## 注意事项

- 视频生成是异步操作，通常需要几分钟时间
- 建议每 10-30 秒轮询一次操作状态
- 生成的视频以 base64 编码形式返回
- 请确保有足够的配额和权限访问 Vertex AI API

## 错误处理

- 400: 请求参数错误
- 401: 认证失败
- 403: 权限不足
- 429: 请求频率过高
- 500: 服务器内部错误

## 示例代码

```javascript
// 启动视频生成
const startGeneration = async () => {
  const response = await fetch('/v1/projects/cognition/locations/us/publishers/google/models/veo-3.0-generate-preview:predictLongRunning', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      instances: [{
        prompt: "A beautiful sunset over the ocean with waves gently crashing on the shore"
      }]
    })
  });
  const result = await response.json();
  return result.name; // 操作名称
};

// 查询操作状态
const checkStatus = async (operationName) => {
  const response = await fetch('/v1/projects/cognition/locations/us/publishers/google/models/veo-3.0-generate-preview:fetchPredictOperation', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ name: operationName })
  });
  return await response.json();
};
```
