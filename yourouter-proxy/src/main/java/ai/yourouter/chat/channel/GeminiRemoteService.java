package ai.yourouter.chat.channel;

import ai.yourouter.chat.channel.response.gemini.GeminiResponse;
import ai.yourouter.chat.channel.response.gemini.GeminiResponseChunk;
import ai.yourouter.chat.channel.response.vertex.LongRunningOperationResponse;
import ai.yourouter.chat.channel.utils.GoogleDomainUtils;
import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.chat.remote.response.BestKeyResponse;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.context.usage.ChatUsage;
import ai.yourouter.common.exception.CognitionWebException;
import ai.yourouter.common.exception.error.OpenAIError;
import ai.yourouter.common.utils.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;

/**
 * Gemini 原生 API 远程服务
 * 处理与 Gemini 原生 API 的通信
 */
@Slf4j
@Service
@SuppressWarnings({"DuplicatedCode", "unused"})
public class GeminiRemoteService extends LlmRemoteService {

    // 常量定义
    private static final String PROJECT_ID_FIELD = "projectId";

    @Qualifier("httpWebClient")
    private final WebClient httpWebClient;

    @Qualifier("sseWebClient")
    private final WebClient sseWebClient;

    public GeminiRemoteService(KmgRemoteService kmgRemoteService,
                               @Qualifier("httpWebClient") WebClient httpWebClient,
                               @Qualifier("sseWebClient") WebClient sseWebClient,
                               ObjectMapper objectMapper) {
        super(kmgRemoteService);
        this.httpWebClient = httpWebClient;
        this.sseWebClient = sseWebClient;
    }

    /**
     * 构建 Gemini 原生 API 请求的域名 URL
     *
     * @param chatContext 聊天上下文
     * @param isStream    是否为流式请求
     * @return 完整的 API URL
     */
    private String buildGeminiApiUrl(ChatContext chatContext, boolean isStream) {
        BestKeyResponse keyInfo = chatContext.getKeyInfo();
        if (keyInfo == null) {
            throw new CognitionWebException("Key info is null in chat context");
        }

        String projectId = keyInfo.getMetadata().get(PROJECT_ID_FIELD);
        if (projectId == null) {
            throw new CognitionWebException("Project ID is required for Gemini models");
        }

        String modelName = chatContext.apiModelName();
        if (isStream) {
            return GoogleDomainUtils.buildGeminiNativeStreamUrl(projectId, modelName);
        } else {
            return GoogleDomainUtils.buildGeminiNativeUrl(projectId, modelName);
        }
    }

    /**
     * 准备 Gemini 请求体
     *
     * @param body        原始请求体
     * @param chatContext 聊天上下文
     */
    private void prepareGeminiRequestBody(HashMap<String, Object> body, ChatContext chatContext) {
        // 模型名称已经在 URL 中指定
        body.remove("model");
    }

    /**
     * 流式 Gemini 消息完成
     */
    public Flux<ServerSentEvent<String>> streamGeminiCompletion(HashMap<String, Object> body, ChatContext chatContext) {
        var isFirst = new AtomicBoolean(true);
        prepareGeminiRequestBody(body, chatContext);

        return Mono.just(chatContext.getKeyInfo())
                .flatMapMany(key -> createStreamRequest(body, chatContext, key, isFirst));
    }

    /**
     * 创建流式请求
     */
    private Flux<ServerSentEvent<String>> createStreamRequest(HashMap<String, Object> body,
                                                              ChatContext chatContext,
                                                              BestKeyResponse key,
                                                              AtomicBoolean isFirst) {
        String apiUrl = buildGeminiApiUrl(chatContext, true);

        return sseWebClient.post()
                .uri(apiUrl)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + key.getToken())
                .header(HttpHeaders.CONTENT_TYPE, "application/json")
                .bodyValue(body)
                .retrieve()
                .bodyToFlux(String.class)
                .map(processStreamResponse(chatContext, isFirst))
                .map(response -> ServerSentEvent.<String>builder().data(response).build())
                .doOnComplete(() -> {
                    if (chatContext.getChatUsage() != null && chatContext.getChatUsage().needCounterBySelf()) {
                        log.warn("触发手动计费 | 原因: stream未返回usage内容 | 用户ID: {} | 模型: {} | 密钥: {}",
                                chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(),
                                chatContext.getKeyInfo().getToken());
                        // TODO: 实现手动计费逻辑
                    }
                })
                .doOnError(error -> {
                    loggingError(error, chatContext);
                    handleApiError(error, key, chatContext);
                });
    }

    /**
     * 处理流式响应数据
     */
    private Function<String, String> processStreamResponse(ChatContext chatContext, AtomicBoolean isFirst) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            if (Objects.equals(message.trim(), "[DONE]")) {
                log.debug("流式请求完成 | 状态: DONE | 用户ID: {} | 模型: {}", userId, modelName);
                return message;
            }

            if (isFirst.getAndSet(false)) {
                chatContext.getChatRequestStatistic().setPreparationTime(Instant.now().toEpochMilli());
                log.debug("收到首个数据块 | 用户ID: {} | 模型: {}", userId, modelName);
            }

            // 收集流式响应内容到 streamResponse
            if (!message.trim().isEmpty()) {
                chatContext.getChatRequestStatistic().getStreamResponse().append(message).append("\n");
            }

            try {
                GeminiResponseChunk chunk = JsonUtils.parseObject(message, GeminiResponseChunk.class);

                log.debug("接收数据块 | 用户ID: {} | 模型: {} | 消息内容: {}",
                        userId, modelName, message);

                // 更新使用量统计
                if (chunk.getUsageMetadata() != null && chunk.getUsageMetadata().getTotalTokenCount() != 0) {
                    chatContext.setChatUsage(ChatUsage.create(chunk.getUsageMetadata()));
                    log.info("更新使用情况 | 请求类型: 流式 | 用户ID: {} | 模型: {} | 使用量: {}",
                            userId, modelName, JsonUtils.toJSONString(chunk.getUsageMetadata()));
                }

                log.debug("数据块解析完成 | 用户ID: {} | 模型: {} | 响应内容: {}",
                        userId, modelName, message);

                // 使用父类的共性方法添加vendor和ID信息
                return processStreamWithVendorAndId(message, chatContext);
            } catch (Exception e) {
                log.error("数据块解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException(JsonUtils.toJSONString(OpenAIError.serviceUnavailable()));
            }
        };
    }

    /**
     * 非流式 Gemini 消息完成
     */
    public Mono<Object> nonStreamGeminiCompletion(HashMap<String, Object> body, ChatContext chatContext) {
        prepareGeminiRequestBody(body, chatContext);

        return Mono.just(chatContext.getKeyInfo())
                .flatMap(key -> createNonStreamRequest(body, chatContext, key));
    }

    /**
     * 创建非流式请求
     */
    private Mono<Object> createNonStreamRequest(HashMap<String, Object> body,
                                                ChatContext chatContext,
                                                BestKeyResponse key) {
        String apiUrl = buildGeminiApiUrl(chatContext, false);

        return httpWebClient.post()
                .uri(apiUrl)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + key.getToken())
                .header(HttpHeaders.CONTENT_TYPE, "application/json")
                .bodyValue(body)
                .retrieve()
                .bodyToMono(String.class)
                .map(processNonStreamResponse(chatContext))
                .doOnError(error -> {
                    loggingError(error, chatContext);
                    handleApiError(error, key, chatContext);
                });
    }

    /**
     * 处理非流式响应数据
     */
    private Function<String, Object> processNonStreamResponse(ChatContext chatContext) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            try {
                log.debug("接收非流式响应 | 用户ID: {} | 模型: {} | 消息内容: {}",
                        userId, modelName, message);

                GeminiResponse response = JsonUtils.parseObject(message, GeminiResponse.class);

                // 更新使用量统计
                if (response.getUsageMetadata() != null) {
                    chatContext.setChatUsage(ChatUsage.create(response.getUsageMetadata()));
                    log.info("更新使用情况 | 请求类型: 非流式 | 用户ID: {} | 模型: {} | 使用量: {}",
                            userId, modelName, JsonUtils.toJSONString(response.getUsageMetadata()));
                }

                // 使用父类的共性方法添加vendor和ID信息
                return processNonStreamWithVendorAndId(message, chatContext);
            } catch (Exception e) {
                log.error("响应解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException(JsonUtils.toJSONString(OpenAIError.serviceUnavailable()));
            }
        };
    }

    /**
     * 处理 API 错误
     */
    private void handleApiError(Throwable error, BestKeyResponse key, ChatContext chatContext) {
        // 这里可以添加特定的错误处理逻辑
        // 例如标记密钥为不可用等
        log.error("Gemini API 调用错误 | 密钥: {} | 错误: {}", key.getToken(), error.getMessage());
    }

    /**
     * 构建 Vertex AI 长时间运行操作的 URL
     *
     * @param chatContext 聊天上下文
     * @return 完整的 API URL
     */
    private String buildVertexAiPredictLongRunningUrl(ChatContext chatContext) {
        BestKeyResponse keyInfo = chatContext.getKeyInfo();
        if (keyInfo == null) {
            throw new CognitionWebException("Key info is null in chat context");
        }

        String projectId = keyInfo.getMetadata().get(PROJECT_ID_FIELD);
        if (projectId == null) {
            throw new CognitionWebException("Project ID is required for Vertex AI models");
        }

        String modelName = chatContext.apiModelName();
        return GoogleDomainUtils.buildVertexAiPredictLongRunningUrl(projectId, modelName);
    }

    /**
     * 构建 Vertex AI 操作状态查询的 URL
     *
     * @param operationName 操作名称
     * @return 完整的 API URL
     */
    private String buildVertexAiOperationUrl(String operationName) {
        return GoogleDomainUtils.buildVertexAiOperationUrl(operationName);
    }

    /**
     * 启动长时间运行的预测操作（如视频生成）
     */
    public Mono<Object> predictLongRunning(HashMap<String, Object> body, ChatContext chatContext) {
        return Mono.just(chatContext.getKeyInfo())
                .flatMap(key -> createPredictLongRunningRequest(body, chatContext, key));
    }

    /**
     * 创建长时间运行的预测请求
     */
    private Mono<Object> createPredictLongRunningRequest(HashMap<String, Object> body,
                                                         ChatContext chatContext,
                                                         BestKeyResponse key) {
        String apiUrl = buildVertexAiPredictLongRunningUrl(chatContext);

        return httpWebClient.post()
                .uri(apiUrl)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + key.getToken())
                .header(HttpHeaders.CONTENT_TYPE, "application/json")
                .bodyValue(body)
                .retrieve()
                .bodyToMono(String.class)
                .map(processPredictLongRunningResponse(chatContext))
                .doOnError(error -> {
                    loggingError(error, chatContext);
                    handleApiError(error, key, chatContext);
                });
    }

    /**
     * 处理长时间运行预测响应数据
     */
    private Function<String, Object> processPredictLongRunningResponse(ChatContext chatContext) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            try {
                log.debug("接收长时间运行操作响应 | 用户ID: {} | 模型: {} | 消息内容: {}",
                        userId, modelName, message);

                LongRunningOperationResponse response = JsonUtils.parseObject(message, LongRunningOperationResponse.class);

                log.info("长时间运行操作已启动 | 用户ID: {} | 模型: {} | 操作名称: {}",
                        userId, modelName, response.getName());

                chatContext.setChatUsage(ChatUsage.veo3());
                // 使用父类的共性方法添加vendor和ID信息
                return processNonStreamWithVendorAndId(message, chatContext);
            } catch (Exception e) {
                log.error("长时间运行操作响应解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException(JsonUtils.toJSONString(OpenAIError.serviceUnavailable()));
            }
        };
    }

    /**
     * 查询长时间运行操作的状态
     */
    public Mono<Object> fetchPredictOperation(String operationName, ChatContext chatContext) {
        return Mono.just(chatContext.getKeyInfo())
                .flatMap(key -> createFetchOperationRequest(operationName, chatContext, key));
    }

    /**
     * 创建操作状态查询请求
     */
    private Mono<Object> createFetchOperationRequest(String operationName,
                                                     ChatContext chatContext,
                                                     BestKeyResponse key) {
        String apiUrl = buildVertexAiOperationUrl(operationName);

        return httpWebClient.get()
                .uri(apiUrl)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + key.getToken())
                .retrieve()
                .bodyToMono(String.class)
                .map(processFetchOperationResponse(chatContext))
                .doOnError(error -> {
                    loggingError(error, chatContext);
                    handleApiError(error, key, chatContext);
                });
    }

    /**
     * 处理操作状态查询响应数据
     */
    private Function<String, Object> processFetchOperationResponse(ChatContext chatContext) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            try {
                log.debug("接收操作状态查询响应 | 用户ID: {} | 模型: {} | 消息内容: {}",
                        userId, modelName, message);

                LongRunningOperationResponse response = JsonUtils.parseObject(message, LongRunningOperationResponse.class);

                log.info("操作状态查询完成 | 用户ID: {} | 模型: {} | 操作完成: {} | 操作名称: {}",
                        userId, modelName, response.getDone(), response.getName());

                // 使用父类的共性方法添加vendor和ID信息
                return processNonStreamWithVendorAndId(message, chatContext);
            } catch (Exception e) {
                log.error("操作状态查询响应解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException(JsonUtils.toJSONString(OpenAIError.serviceUnavailable()));
            }
        };
    }
}
