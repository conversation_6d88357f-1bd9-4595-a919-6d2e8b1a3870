package ai.yourouter.chat.channel.response.openai;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder({"id", "object", "created", "model", "choices", "usage", "service_tier", "system_fingerprint"})
public class ChatCompletion {

    @JsonProperty("id")
    private String id;
    @JsonProperty("object")
    private String object;
    @JsonProperty("created")
    private Integer created;
    @JsonProperty("model")
    private String model;
    @JsonProperty("system_fingerprint")
    private String systemFingerprint;
    @JsonProperty("choices")
    private List<ChatCompletionChoice> choices;
    @JsonProperty("service_tier")
    private String serviceTier = "default";
    @JsonProperty("usage")
    private Usage usage;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"index", "message", "logprobs", "finish_reason"})
    public static class ChatCompletionChoice {
        @JsonProperty("index")
        private Integer index;
        @JsonProperty("message")
        private ChatMessage message;
        @JsonProperty("logprobs")
        @JsonInclude(JsonInclude.Include.ALWAYS)
        private LogProbs logprobs;
        @JsonProperty("finish_reason")
        private String finishReason;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"role", "reasoning_content", "content", "refusal", "tool_calls", "function_call", "audio"})
    public static class ChatMessage {
        @JsonProperty("role")
        private String role;
        @JsonProperty("content")
        private String content;

        @JsonProperty("reasoning_content")
        private String reasoningContent;

        @JsonProperty("refusal")
        @JsonInclude(JsonInclude.Include.ALWAYS)
        private String refusal;
        @JsonProperty("tool_calls")
        private List<ToolCall> toolCalls;
        @JsonProperty("function_call")
        @Deprecated
        private FunctionCall functionCall;
        @JsonProperty("audio")
        private AudioResponse audio;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"id", "type", "function"})
    public static class ToolCall {
        @JsonProperty("id")
        private String id;
        @JsonProperty("type")
        private String type;
        @JsonProperty("function")
        private Function function;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"name", "arguments"})
    public static class Function {
        @JsonProperty("name")
        private String name;
        @JsonProperty("arguments")
        private String arguments;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"name", "arguments"})
    public static class FunctionCall {
        @JsonProperty("name")
        private String name;
        @JsonProperty("arguments")
        private String arguments;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"id", "expires_at", "data", "transcript"})
    public static class AudioResponse {
        @JsonProperty("id")
        private String id;
        @JsonProperty("expires_at")
        private Integer expiresAt;
        @JsonProperty("data")
        private String data;
        @JsonProperty("transcript")
        private String transcript;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"content", "refusal"})
    public static class LogProbs {
        @JsonProperty("content")
        private List<ContentLogprob> content;
        @JsonProperty("refusal")
        private List<RefusalLogprob> refusal;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"token", "logprob", "bytes", "top_logprobs"})
    public static class ContentLogprob {
        @JsonProperty("token")
        private String token;
        @JsonProperty("logprob")
        private Double logprob;
        @JsonProperty("bytes")
        private List<Integer> bytes;
        @JsonProperty("top_logprobs")
        private List<TopLogprob> topLogprobs;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"token", "logprob", "bytes", "top_logprobs"})
    public static class RefusalLogprob {
        @JsonProperty("token")
        private String token;
        @JsonProperty("logprob")
        private Double logprob;
        @JsonProperty("bytes")
        private List<Integer> bytes;
        @JsonProperty("top_logprobs")
        private List<TopLogprob> topLogprobs;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"token", "logprob"})
    public static class TopLogprob {
        @JsonProperty("token")
        private String token;
        @JsonProperty("logprob")
        private Double logprob;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"prompt_tokens", "completion_tokens", "total_tokens", "prompt_tokens_details", "completion_tokens_details"})
    public static class Usage {

        @JsonProperty("num_sources_used")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer numSourcesUsed;
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;
        @JsonProperty("completion_tokens")
        private Integer completionTokens;
        @JsonProperty("total_tokens")
        private Integer totalTokens;
        @JsonProperty("prompt_tokens_details")
        private PromptTokensDetails promptTokensDetails = new PromptTokensDetails();
        @JsonProperty("completion_tokens_details")
        private CompletionTokensDetails completionTokensDetails = new CompletionTokensDetails();

        public void removeCache() {
            promptTokensDetails.setCachedTokens(0);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"reasoning_tokens", "audio_tokens", "accepted_prediction_tokens", "rejected_prediction_tokens"})
    public static class CompletionTokensDetails {
        @JsonProperty("reasoning_tokens")
        private Integer reasoningTokens = 0;
        @JsonProperty("audio_tokens")
        private Integer audioTokens = 0;
        @JsonProperty("accepted_prediction_tokens")
        private Integer acceptedPredictionTokens = 0;
        @JsonProperty("rejected_prediction_tokens")
        private Integer rejectedPredictionTokens = 0;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonPropertyOrder({"cached_tokens", "audio_tokens"})
    public static class PromptTokensDetails {
        @JsonProperty("audio_tokens")
        private Integer audioTokens = 0;
        @JsonProperty("cached_tokens")
        private Integer cachedTokens = 0;
        //grok
        @JsonProperty("image_tokens")
        private Integer imageTokens = 0;
        @JsonProperty("text_tokens")
        private Integer textTokens = 0;
    }
}