package ai.yourouter.chat.channel.response.vertex;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Google Cloud Long Running Operation 响应模型
 * 用于处理长时间运行的操作，如视频生成等
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LongRunningOperationResponse {

    /**
     * 操作名称，用于后续查询操作状态
     * 格式: projects/{project}/locations/{location}/operations/{operation-id}
     */
    @JsonProperty("name")
    private String name;

    /**
     * 操作元数据，包含操作的详细信息
     */
    @JsonProperty("metadata")
    private OperationMetadata metadata;

    /**
     * 操作是否完成
     */
    @JsonProperty("done")
    private Boolean done;

    /**
     * 操作结果（当done为true时）
     */
    @JsonProperty("response")
    private Object response;

    /**
     * 操作错误信息（当操作失败时）
     */
    @JsonProperty("error")
    private OperationError error;

    /**
     * 操作元数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OperationMetadata {
        
        /**
         * 操作类型
         */
        @JsonProperty("@type")
        private String type;

        /**
         * 创建时间
         */
        @JsonProperty("createTime")
        private String createTime;

        /**
         * 更新时间
         */
        @JsonProperty("updateTime")
        private String updateTime;

        /**
         * 操作状态
         */
        @JsonProperty("state")
        private String state;

        /**
         * 进度百分比
         */
        @JsonProperty("progressPercentage")
        private Integer progressPercentage;

        /**
         * 通用标签
         */
        @JsonProperty("genericMetadata")
        private Object genericMetadata;
    }

    /**
     * 操作错误信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OperationError {
        
        /**
         * 错误代码
         */
        @JsonProperty("code")
        private Integer code;

        /**
         * 错误消息
         */
        @JsonProperty("message")
        private String message;

        /**
         * 错误详情
         */
        @JsonProperty("details")
        private Object details;
    }
}
