package ai.yourouter.chat.channel.response.vertex;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Vertex AI 视频生成请求模型
 * 用于 Veo 3.0 等视频生成模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VideoGenerationRequest {

    /**
     * 实例列表，包含生成参数
     */
    @JsonProperty("instances")
    private List<Instance> instances;

    /**
     * 生成参数
     */
    @JsonProperty("parameters")
    private Parameters parameters;

    /**
     * 实例
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Instance {
        
        /**
         * 文本提示词
         */
        @JsonProperty("prompt")
        private String prompt;

        /**
         * 负面提示词
         */
        @JsonProperty("negativePrompt")
        private String negativePrompt;

        /**
         * 参考图像
         */
        @JsonProperty("referenceImage")
        private ReferenceImage referenceImage;

        /**
         * 参考视频
         */
        @JsonProperty("referenceVideo")
        private ReferenceVideo referenceVideo;
    }

    /**
     * 参考图像
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ReferenceImage {
        
        /**
         * 图像数据（base64编码）
         */
        @JsonProperty("bytesBase64Encoded")
        private String bytesBase64Encoded;

        /**
         * 图像MIME类型
         */
        @JsonProperty("mimeType")
        private String mimeType;
    }

    /**
     * 参考视频
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ReferenceVideo {
        
        /**
         * 视频数据（base64编码）
         */
        @JsonProperty("bytesBase64Encoded")
        private String bytesBase64Encoded;

        /**
         * 视频MIME类型
         */
        @JsonProperty("mimeType")
        private String mimeType;
    }

    /**
     * 生成参数
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Parameters {
        
        /**
         * 视频长度（秒）
         */
        @JsonProperty("duration")
        private Integer duration;

        /**
         * 宽高比
         */
        @JsonProperty("aspectRatio")
        private String aspectRatio;

        /**
         * 随机种子
         */
        @JsonProperty("seed")
        private Long seed;

        /**
         * 生成质量
         */
        @JsonProperty("quality")
        private String quality;

        /**
         * 帧率
         */
        @JsonProperty("frameRate")
        private Integer frameRate;

        /**
         * 运动强度
         */
        @JsonProperty("motionIntensity")
        private String motionIntensity;

        /**
         * 相机运动
         */
        @JsonProperty("cameraMotion")
        private String cameraMotion;
    }
}
