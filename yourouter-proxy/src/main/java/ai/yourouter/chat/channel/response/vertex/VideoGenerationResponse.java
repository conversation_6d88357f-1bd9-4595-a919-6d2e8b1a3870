package ai.yourouter.chat.channel.response.vertex;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Vertex AI 视频生成响应模型
 * 用于 Veo 3.0 等视频生成模型的响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VideoGenerationResponse {

    /**
     * 预测结果列表
     */
    @JsonProperty("predictions")
    private List<Prediction> predictions;

    /**
     * 部署的资源名称
     */
    @JsonProperty("deployedModelId")
    private String deployedModelId;

    /**
     * 模型名称
     */
    @JsonProperty("model")
    private String model;

    /**
     * 模型显示名称
     */
    @JsonProperty("modelDisplayName")
    private String modelDisplayName;

    /**
     * 模型版本ID
     */
    @JsonProperty("modelVersionId")
    private String modelVersionId;

    /**
     * 预测结果
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Prediction {
        
        /**
         * 生成的视频数据（base64编码）
         */
        @JsonProperty("bytesBase64Encoded")
        private String bytesBase64Encoded;

        /**
         * 视频MIME类型
         */
        @JsonProperty("mimeType")
        private String mimeType;

        /**
         * 视频时长（秒）
         */
        @JsonProperty("duration")
        private Double duration;

        /**
         * 视频宽度
         */
        @JsonProperty("width")
        private Integer width;

        /**
         * 视频高度
         */
        @JsonProperty("height")
        private Integer height;

        /**
         * 帧率
         */
        @JsonProperty("frameRate")
        private Double frameRate;

        /**
         * 文件大小（字节）
         */
        @JsonProperty("sizeBytes")
        private Long sizeBytes;

        /**
         * 生成的提示词（如果有修改）
         */
        @JsonProperty("generatedPrompt")
        private String generatedPrompt;

        /**
         * 安全评级
         */
        @JsonProperty("safetyRatings")
        private List<SafetyRating> safetyRatings;

        /**
         * 生成元数据
         */
        @JsonProperty("metadata")
        private GenerationMetadata metadata;
    }

    /**
     * 安全评级
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SafetyRating {
        
        /**
         * 安全类别
         */
        @JsonProperty("category")
        private String category;

        /**
         * 概率等级
         */
        @JsonProperty("probability")
        private String probability;

        /**
         * 是否被阻止
         */
        @JsonProperty("blocked")
        private Boolean blocked;
    }

    /**
     * 生成元数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GenerationMetadata {
        
        /**
         * 使用的种子值
         */
        @JsonProperty("seed")
        private Long seed;

        /**
         * 生成时间（毫秒）
         */
        @JsonProperty("generationTime")
        private Long generationTime;

        /**
         * 模型版本
         */
        @JsonProperty("modelVersion")
        private String modelVersion;
    }
}
