package ai.yourouter.chat.channel.utils;

public class GoogleDomainUtils {

    public static String buildGeminiOpenAIUrl(String projectId) {
        return "https://us-central1-aiplatform.googleapis.com/v1/projects/%s/locations/us-central1/endpoints/openapi/chat/completions".formatted(projectId);
    }

    /**
     * 构建 Gemini 原生 API URL (非流式)
     */
    public static String buildGeminiNativeUrl(String projectId, String model) {
        return "https://us-central1-aiplatform.googleapis.com/v1/projects/%s/locations/us-central1/publishers/google/models/%s:generateContent".formatted(projectId, model);
    }

    /**
     * 构建 Gemini 原生 API URL (流式)
     */
    public static String buildGeminiNativeStreamUrl(String projectId, String model) {
        return "https://us-central1-aiplatform.googleapis.com/v1/projects/%s/locations/us-central1/publishers/google/models/%s:streamGenerateContent?alt=sse".formatted(projectId, model);
    }

    /**
     * 构建 Vertex AI 长时间运行操作 URL (视频生成等)
     */
    public static String buildVertexAiPredictLongRunningUrl(String projectId, String model) {
        return "https://us-central1-aiplatform.googleapis.com/v1/projects/%s/locations/us-central1/publishers/google/models/%s:predictLongRunning".formatted(projectId, model);
    }

    /**
     * 构建 Vertex AI 操作状态查询 URL
     */
    public static String buildVertexAiOperationUrl(String operationName) {
        return "https://us-central1-aiplatform.googleapis.com/v1/%s".formatted(operationName);
    }
}
