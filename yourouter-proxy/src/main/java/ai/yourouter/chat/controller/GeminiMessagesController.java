package ai.yourouter.chat.controller;

import ai.yourouter.chat.service.impl.GeminiMessagesServiceImpl;
import ai.yourouter.chat.util.ResponseUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.CorePublisher;

import java.util.HashMap;

/**
 * Gemini 原生 API 控制器
 * 处理 Gemini 原生格式的请求
 */
@Slf4j
@RestController
@RequestMapping("/v1/projects/cognition/locations/us/publishers/google/models")
@RequiredArgsConstructor
@SuppressWarnings("ReactiveStreamsUnusedPublisher")
public class GeminiMessagesController {

    private final GeminiMessagesServiceImpl geminiMessagesService;

    /**
     * Gemini generateContent API 端点 (非流式)
     * 支持原生 Gemini API 格式
     */
    @PostMapping("/{model}:generateContent")
    public ResponseEntity<? extends CorePublisher<?>> generateContent(
            @PathVariable String model,
            @RequestBody HashMap<String, Object> req) {
        return buildGeminiNonStreamResponse(req);
    }

    /**
     * Gemini streamGenerateContent API 端点 (流式)
     * 支持原生 Gemini API 格式
     */
    @PostMapping("/{model}:streamGenerateContent")
    public ResponseEntity<? extends CorePublisher<?>> streamGenerateContent(
            @PathVariable String model,
            @RequestBody HashMap<String, Object> req) {
        return buildGeminiStreamResponse(req);
    }


    /**
     * Vertex AI 长时间运行预测 API 端点 (视频生成等)
     * 支持 Veo 3.0 等视频生成模型
     */
    @PostMapping("/{model}:predictLongRunning")
    public ResponseEntity<? extends CorePublisher<?>> predictLongRunning(
            @PathVariable String model,
            @RequestBody HashMap<String, Object> req) {
        log.info("接收长时间运行预测请求 | 模型: {} | 请求体: {}", model, req);
        return buildGeminiNonStreamResponse(req, "predictLongRunning");
    }

    /**
     * Vertex AI 操作状态查询 API 端点
     * 用于查询长时间运行操作的状态和结果
     */
    @PostMapping("/{model}:fetchPredictOperation")
    public ResponseEntity<? extends CorePublisher<?>> fetchPredictOperation(
            @PathVariable String model,
            @RequestBody HashMap<String, Object> req) {
        log.info("接收操作状态查询请求 | 模型: {} | 请求体: {}", model, req);

        // 从请求体中提取操作名称
        String operationName = extractOperationName(req);
        if (operationName == null) {
            log.error("操作名称不能为空 | 模型: {} | 请求体: {}", model, req);
            return ResponseUtils.createErrorResponse(HttpStatus.BAD_REQUEST);
        }

        return buildGeminiFetchOperationResponse(operationName, req);
    }

    /**
     * 构建 Gemini 流式响应
     */
    private ResponseEntity<? extends CorePublisher<?>> buildGeminiStreamResponse(HashMap<String, Object> req) {

        return ResponseUtils.createStreamResponse(geminiMessagesService.streamCompletion(req));
    }

    /**
     * 构建 Gemini 非流式响应
     */
    private ResponseEntity<? extends CorePublisher<?>> buildGeminiNonStreamResponse(HashMap<String, Object> req) {
        return ResponseUtils.createNonStreamResponse(geminiMessagesService.completion(req));
    }

    /**
     * 构建 Gemini 非流式响应（支持不同操作类型）
     */
    private ResponseEntity<? extends CorePublisher<?>> buildGeminiNonStreamResponse(HashMap<String, Object> req, String operationType) {
        if ("predictLongRunning".equals(operationType)) {
            return ResponseUtils.createNonStreamResponse(geminiMessagesService.predictLongRunning(req));
        }
        return ResponseUtils.createNonStreamResponse(geminiMessagesService.completion(req));
    }

    /**
     * 构建操作状态查询响应
     */
    private ResponseEntity<? extends CorePublisher<?>> buildGeminiFetchOperationResponse(String operationName, HashMap<String, Object> req) {
        return ResponseUtils.createNonStreamResponse(geminiMessagesService.fetchPredictOperation(operationName, req));
    }

    /**
     * 从请求体中提取操作名称
     */
    private String extractOperationName(HashMap<String, Object> req) {
        if (req.containsKey("name")) {
            return req.get("name").toString();
        }
        if (req.containsKey("operationName")) {
            return req.get("operationName").toString();
        }
        if (req.containsKey("operation_name")) {
            return req.get("operation_name").toString();
        }
        return null;
    }

}
