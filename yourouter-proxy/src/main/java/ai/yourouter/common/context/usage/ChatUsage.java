package ai.yourouter.common.context.usage;

import com.fasterxml.jackson.annotation.JsonInclude;

import ai.yourouter.chat.channel.response.claude.ClaudeMessageTokenStats;
import ai.yourouter.chat.channel.response.gemini.GeminiUsageMetadata;
import ai.yourouter.chat.channel.response.openai.ChatCompletion;
import ai.yourouter.chat.channel.response.openai.ChatCompletionChunk;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ChatUsage {

    private Integer totalTokens;

    /**
     * 总的promptTokens
     */
    private Integer promptTokens;

    /**
     * 总的completionTokens
     */
    private Integer completionTokens;

    private Integer type = 0;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer numSourcesUsed;

    private ChatPromptUsageDetail promptUsageDetail = new ChatPromptUsageDetail();

    private ChatCompletionUsageDetail completionUsageDetail = new ChatCompletionUsageDetail();


    public static ChatUsage createZero() {
        ChatUsage chatUsage = new ChatUsage();
        chatUsage.completionTokens = 0;
        chatUsage.promptTokens = 0;
        chatUsage.totalTokens = 0;
        return chatUsage;
    }


    public static ChatUsage create(ChatCompletionChunk chatCompletionChunk, boolean ignoreCache) {
        var chatUsage = new ChatUsage();
        if (chatCompletionChunk.getUsage().getNumSourcesUsed() != null) {
            chatUsage.numSourcesUsed = chatCompletionChunk.getUsage().getNumSourcesUsed();
        }
        chatUsage.completionTokens = chatCompletionChunk.getUsage().getCompletionTokens();
        chatUsage.promptTokens = chatCompletionChunk.getUsage().getPromptTokens();
        chatUsage.totalTokens = chatCompletionChunk.getUsage().getTotalTokens();
        chatUsage.promptUsageDetail = ChatPromptUsageDetail.from(chatCompletionChunk.getUsage(), ignoreCache);
        chatUsage.completionUsageDetail = ChatCompletionUsageDetail.from(chatCompletionChunk.getUsage());
        return chatUsage;
    }

    public static ChatUsage create(ChatCompletionChunk chatCompletionChunk) {
        var chatUsage = new ChatUsage();
        chatUsage.completionTokens = chatCompletionChunk.getUsage().getCompletionTokens();
        chatUsage.promptTokens = chatCompletionChunk.getUsage().getPromptTokens();
        chatUsage.totalTokens = chatCompletionChunk.getUsage().getTotalTokens();
        chatUsage.promptUsageDetail = ChatPromptUsageDetail.from(chatCompletionChunk.getUsage(), false);
        chatUsage.completionUsageDetail = ChatCompletionUsageDetail.from(chatCompletionChunk.getUsage());
        return chatUsage;
    }

    public static ChatUsage create(ClaudeMessageTokenStats usage) {
        var chatUsage = new ChatUsage();
        var inputAll = usage.getInputTokens() + usage.getCacheCreationInputTokens() + usage.getCacheReadInputTokens();
        chatUsage.totalTokens = inputAll + usage.getOutputTokens();
        chatUsage.promptTokens = inputAll;
        chatUsage.completionTokens = usage.getOutputTokens();
        chatUsage.promptUsageDetail = new ChatPromptUsageDetail(usage.getInputTokens(), usage.getCacheReadInputTokens(), usage.getCacheCreationInputTokens());
        chatUsage.completionUsageDetail = new ChatCompletionUsageDetail(usage.getOutputTokens());
        return chatUsage;
    }

    public static ChatUsage create(ChatCompletion chatCompletion, boolean ignoreCache) {
        var chatUsage = new ChatUsage();
        if (chatCompletion.getUsage().getNumSourcesUsed() != null) {
            chatUsage.numSourcesUsed = chatCompletion.getUsage().getNumSourcesUsed();
        }
        chatUsage.promptTokens = chatCompletion.getUsage().getPromptTokens();
        chatUsage.completionTokens = chatCompletion.getUsage().getCompletionTokens();
        chatUsage.totalTokens = chatCompletion.getUsage().getTotalTokens();
        chatUsage.promptUsageDetail = ChatPromptUsageDetail.from(chatCompletion.getUsage(), ignoreCache);
        chatUsage.completionUsageDetail = ChatCompletionUsageDetail.from(chatCompletion.getUsage());
        return chatUsage;
    }

    public ChatUsage(Integer promptTokens, Integer completionTokens) {
        this.totalTokens = promptTokens + completionTokens;
        this.promptTokens = promptTokens;
        this.completionTokens = completionTokens;
        this.promptUsageDetail = new ChatPromptUsageDetail(promptTokens);
        this.completionUsageDetail = new ChatCompletionUsageDetail(completionTokens);
    }

    public ChatUsage(Integer promptTokens, Integer completionTokens, Integer textCacheTokens, Integer reasoningTokens) {
        this.totalTokens = promptTokens + completionTokens;
        this.promptTokens = promptTokens;
        this.completionTokens = completionTokens;
        this.promptUsageDetail = new ChatPromptUsageDetail(promptTokens - textCacheTokens, textCacheTokens);
        this.completionUsageDetail = new ChatCompletionUsageDetail(completionTokens, reasoningTokens);
    }

    public void updateOutPut(Integer completionTokens) {
        this.completionTokens = completionTokens;
        this.totalTokens = promptTokens + completionTokens;
        this.completionUsageDetail = new ChatCompletionUsageDetail(completionTokens);
    }


    public boolean needCounterBySelf() {
        return totalTokens == 0 || promptTokens == 0 || completionTokens == 0;
    }


    public static ChatUsage create(GeminiUsageMetadata usageMetadata) {
        var chatUsage = new ChatUsage();
        chatUsage.promptTokens = usageMetadata.getPromptTokenCount() + usageMetadata.getCachedContentTokenCount();
        chatUsage.completionTokens = usageMetadata.getCandidatesTokenCount() + usageMetadata.getThoughtsTokenCount();
        chatUsage.totalTokens = usageMetadata.getTotalTokenCount();
        chatUsage.promptUsageDetail = new ChatPromptUsageDetail(usageMetadata.getPromptTokensDetails(), usageMetadata.getCacheTokensDetails());
        chatUsage.completionUsageDetail = new ChatCompletionUsageDetail(usageMetadata.getCandidatesTokensDetails(), usageMetadata.getThoughtsTokenCount());
        return chatUsage;
    }

    public static ChatUsage veo3() {
        var chatUsage = new ChatUsage();
        chatUsage.totalTokens = 8;
        var promptUsageDetail = new ChatCompletionUsageDetail();
        promptUsageDetail.setAudioTokens(8);
        chatUsage.completionUsageDetail = promptUsageDetail;
        return chatUsage;
    }
}

