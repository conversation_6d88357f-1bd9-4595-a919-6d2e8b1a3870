package ai.yourouter.common.utils;


import ai.yourouter.common.context.ChatContext;
import ai.yourouter.jpa.organization.statistics.bean.OrganizationLLMStatistics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;

import java.time.Instant;

@Slf4j
public class CharacterLLMStatisticsConverter {

    @Nullable
    public static OrganizationLLMStatistics convert(ChatContext chatContext) {
        var usage = chatContext.getChatUsage();
        if (usage == null) {
            return null;
        }
        var end = Instant.now().toEpochMilli();
        var searchToolCall = 0L;
        if (chatContext.getChatUsage().getNumSourcesUsed() != null) {
            searchToolCall = chatContext.getChatUsage().getNumSourcesUsed();
        } else {
            searchToolCall = chatContext.getChatRequestStatistic().isSearchRequest() ? 1L : 0L;
        }
        return OrganizationLLMStatistics.builder()
                .requestId(chatContext.getRequestId())
                .organizationId(chatContext.getChatUserInfo().getCharactersId())
                .systemModelId(chatContext.getChatModelInfo().getSystemModelId())
                .textPrompt(Long.valueOf(usage.getPromptUsageDetail().getTextTokens()))
                .textCachePrompt(Long.valueOf(usage.getPromptUsageDetail().getCachedTextTokens()))
                .textCachePromptWrite1H(Long.valueOf(usage.getPromptUsageDetail().getCacheTextWriteTokens()))
                .textCachePromptWrite5M(0L)
                .textCompletion(Long.valueOf(usage.getCompletionUsageDetail().getTextTokens()))
                .audioPrompt(Long.valueOf(usage.getPromptUsageDetail().getAudioTokens()))
                .audioCachePrompt(Long.valueOf(usage.getPromptUsageDetail().getCachedAudioTokens()))
                .audioCompletion(Long.valueOf(usage.getCompletionUsageDetail().getAudioTokens()))
                .reasoningCompletion(Long.valueOf(usage.getCompletionUsageDetail().getReasoningTokens()))
                .imagePrompt(Long.valueOf(usage.getPromptUsageDetail().getImageTokens()))
                .imageCachePrompt(Long.valueOf(usage.getPromptUsageDetail().getCachedImageTokens()))
                .imageCompletion(Long.valueOf(usage.getCompletionUsageDetail().getImageTokens()))
                .createTime(end)
                .requestTime(chatContext.getChatRequestStatistic().getStartRequestTime())
                .systemVendor(chatContext.getKeyInfo().getChannel())
                .responseTime(end)
                .durationMs(end - chatContext.getChatRequestStatistic().getStartRequestTime())
                .keyId(chatContext.getKeyInfo().getId())
                .searchTool(searchToolCall)
                .build();
    }
}
