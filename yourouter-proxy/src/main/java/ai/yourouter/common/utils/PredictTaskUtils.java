package ai.yourouter.common.utils;

import ai.yourouter.common.context.ChatContext;
import ai.yourouter.jpa.prediction.task.bean.PredictionTask;

import java.time.ZonedDateTime;
import java.util.LinkedHashMap;
import java.util.Map;

public class PredictTaskUtils {


    public static PredictionTask buildPredictionTask(ChatContext chatContext, LinkedHashMap linkedHashMap,Long id) {
        var keyInfo = chatContext.getKeyInfo();
        var name = linkedHashMap.get("name").toString();
        return PredictionTask.builder()
                .id(id)
                .organizationId(chatContext.getChatUserInfo().getCharactersId())
                .modelName(chatContext.apiModelName())
                .name(name)
                .requestBody(JsonUtils.toJSONString(Map.of("operationName", name)))
                .responseBody(JsonUtils.toJSONString(Map.of("name", name)))
                .keyId(keyInfo.getId())
                .createTime(ZonedDateTime.now())
                .status(PredictionTask.Status.RUNNING)
                .build();
    }
}
